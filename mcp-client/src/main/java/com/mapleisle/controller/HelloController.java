package com.mapleisle.controller;

import com.mapleisle.service.ChatService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/hello")
public class HelloController {

    @Resource
    private  ChatService chatService;
    
    /**
     * @MethodName: hello
     * @Description: TODO
     * @Param:
     * @Return: java.lang.String
     * @Author: mapleisle
     * @Date: 2025/8/18
    **/
    @GetMapping("/world")
    public String hello(){
        return "枫知屿";
    }

    @GetMapping("/chat")
    public String chat(String msg){
        if (msg == null || msg.trim().isEmpty()) {
            msg = "Hello, how are you?";
        }
        return chatService.chatTest(msg);
    }
}
