package com.mapleisle.service.impl;

import com.mapleisle.service.ChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;

/**
 * @ClassName ChatServiceImpl
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/8/18 17:16
 * @Version V1.0
 **/
@Service
@Slf4j
public class ChatServiceImpl implements ChatService {

    private final ChatClient chatClient;

    public ChatServiceImpl(ChatClient.Builder chatClientBuilder) {
        this.chatClient = chatClientBuilder
                .defaultSystem("You are a helpful assistant. You will be given a task to complete. ").build();
    }

    @Override
    public String chatTest(String prompt) {
        log.info("Sending prompt to AI: {}", prompt);
        try {
            String response = chatClient.prompt(prompt).call().content();
            log.info("Received response from AI: {}", response);
            return response;
        } catch (Exception e) {
            log.error("Error calling AI service", e);
            throw e;
        }
    }
}
