package com.mapleisle.service.impl;

import com.mapleisle.service.ChatService;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;

/**
 * @ClassName ChatServiceImpl
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/8/18 17:16
 * @Version V1.0
 **/
@Service
public class ChatServiceImpl implements ChatService {

    private final ChatClient chatClient;

    public ChatServiceImpl(ChatClient.Builder chatClientBuilder) {
        this.chatClient = chatClientBuilder
                .defaultSystem("You are a helpful assistant. You will be given a task to complete. ").build();
    }

    @Override
    public String chatTest(String prompt) {
        return  chatClient.prompt(prompt).call().content();
    }
}
