server:
  port: 7070
spring:
  application:
    name: springai-mcp-client
  profiles:
    active: dev
  ai:
    openai:
      api-key: ${deepseek.key}
      base-url: ${deepseek.url}
      chat:
        options:
          model: ${deepseek.model}

logging:
  level:
    root: info
    org.springframework.ai: debug
    org.springframework.web.client: debug

jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    password: msapadksoadja11
    iv-generator-classname: org.jasypt.iv.NoIvGenerator


