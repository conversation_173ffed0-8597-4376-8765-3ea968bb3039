server:
  port: 7070
spring:
  application:
    name: springai-mcp-client
  profiles:
    active: dev
  ai:
    openai:
      api-key: ${deepseek.key}
      base-url: ${deepseek.url}
      chat:
        options:
          model: ${deepseek.model}
logging:
  level:
    root: info
jasypt:
  encryptor:
    algorithm: PBEWITHHMACSHA512ANDAES_256
    password: mapewewelm11332
    iv-generator-classname: org.jasypt.iv.RandomIvGenerator
    property:
      prefix: ENC(
      suffix: )


