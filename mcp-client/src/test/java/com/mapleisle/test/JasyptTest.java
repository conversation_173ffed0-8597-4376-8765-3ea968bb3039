package com.mapleisle.test;

import org.jasypt.encryption.StringEncryptor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @ClassName JasyptTest
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/8/18 16:41
 * @Version V1.0
 **/
@SpringBootTest
public class JasyptTest {
    @Autowired
    private  StringEncryptor stringEncryptor;
    @Value("${deepseek.key}")
    private String key;

    @Test
    public void encryptor(){
        String encryptKey = stringEncryptor.encrypt(key);
        System.out.println(encryptKey);
        String decrypt = stringEncryptor.decrypt(encryptKey);
        System.out.println(decrypt);
    }
}
