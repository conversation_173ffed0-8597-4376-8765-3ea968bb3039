package com.mapleisle;

import org.jasypt.encryption.StringEncryptor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @ClassName Test01
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/8/18 17:02
 * @Version V1.0
 **/
@SpringBootTest
public class Test01 {
    @Autowired
    private StringEncryptor stringEncryptor;
    @Test
    public void test01() {
        System.out.println("Hello World");
        String encrypt = stringEncryptor.encrypt("sk-75bcf2a2d47c4ba2932ccf700edfdfc8");
        System.out.println(encrypt);
    }
}
